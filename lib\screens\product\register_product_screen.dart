import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import 'package:image/image.dart' as img;

import '../../providers/product_provider.dart';
import '../../providers/seller_provider.dart';
import '../../providers/category_provider.dart';
import '../../providers/unified_workspace_provider.dart';
import '../../utils/event_workspace_utils.dart';
import '../../models/product.dart';
import '../../models/seller.dart';
import '../../widgets/image_crop_widget.dart';
import '../../widgets/product_image.dart';
import '../../utils/toast_utils.dart';
import '../../utils/logger_utils.dart';
import '../../utils/error_utils.dart';
import '../../utils/image_utils.dart';

class RegisterProductScreen extends ConsumerStatefulWidget {
  final Product? product;
  final int? productId;
  final bool isEditing;

  const RegisterProductScreen({
    super.key,
    this.product,
    this.productId,
    this.isEditing = false,
  });

  @override
  ConsumerState<RegisterProductScreen> createState() =>
      _RegisterProductScreenState();
}

class _RegisterProductScreenState extends ConsumerState<RegisterProductScreen> {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _priceController = TextEditingController();
  final TextEditingController _quantityController = TextEditingController();
  final TextEditingController _sellerNameController = TextEditingController();

  // FocusNode 추가
  final FocusNode _nameFocusNode = FocusNode();
  final FocusNode _priceFocusNode = FocusNode();
  final FocusNode _quantityFocusNode = FocusNode();

  Product? _currentProduct;
  XFile? _selectedImage;
  String? _selectedSellerName;
  int? _selectedCategoryId; // 선택된 카테고리 ID
  bool _categoryInitialized = false; // 카테고리 초기화 완료 플래그
  bool _isLoading = false;
  String? _croppedImagePath;
  String? _originalPaddedImagePath; // 원본 패딩 이미지 경로 저장

  final ImagePicker _picker = ImagePicker();

  @override
  void initState() {
    super.initState();
    LoggerUtils.methodStart('initState', tag: 'RegisterProductScreen');

    // 기존 상품이 있는 경우 데이터 로드
    if (widget.product != null) {
      _currentProduct = widget.product;
      _nameController.text = widget.product!.name;
      _priceController.text = widget.product!.price.toString();
      _quantityController.text = widget.product!.quantity.toString();
      _selectedSellerName = widget.product!.sellerName; // 드롭다운용 변수 설정
      _sellerNameController.text = widget.product!.sellerName ?? '';
      _selectedCategoryId = widget.product!.categoryId; // 기존 상품의 카테고리 ID
      _categoryInitialized = true; // 기존 상품이므로 카테고리 이미 설정됨
      
      // 기존 상품의 이미지 정보 설정
      if (widget.product!.imagePath != null && widget.product!.imagePath!.isNotEmpty) {
        _croppedImagePath = widget.product!.imagePath; // 기존 이미지를 크롭된 이미지로 설정
      }
    } else {
      // 새 상품 등록 시에는 카테고리 로드 후 설정
      _categoryInitialized = false;
      
      // 새 상품 등록 시 대표 판매자를 기본값으로 설정
      WidgetsBinding.instance.addPostFrameCallback((_) {
        final sellerState = ref.read(sellerNotifierProvider);
        if (sellerState.defaultSeller != null) {
          setState(() {
            _selectedSellerName = sellerState.defaultSeller!.name;
          });
        }
      });
    }
    
    // 카테고리 목록 로드
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(categoryNotifierProvider.notifier).loadCategories();
    });

    LoggerUtils.methodEnd('initState', tag: 'RegisterProductScreen');
  }

  @override
  void dispose() {
    _nameController.dispose();
    _priceController.dispose();
    _quantityController.dispose();
    _sellerNameController.dispose();
    
    // FocusNode 해제
    _nameFocusNode.dispose();
    _priceFocusNode.dispose();
    _quantityFocusNode.dispose();
    
    super.dispose();
  }

  Future<void> _selectImage() async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 85,
      );

      if (image != null) {
        // 1. 흰색 캔버스+중앙 배치 적용
        final originalBytes = await image.readAsBytes();
        final paddedBytes = await addWhitePaddingAndCenterImage(originalBytes);
        final tempDir = await getTemporaryDirectory();
        final paddedPath = path.join(tempDir.path, 'padded_${DateTime.now().millisecondsSinceEpoch}.jpg');
        final paddedFile = await File(paddedPath).writeAsBytes(paddedBytes);

        // 원본 패딩 이미지 경로 저장 (재크롭을 위해)
        _originalPaddedImagePath = paddedFile.path;

        // 2. 크롭 다이얼로그(라운드 사각형, 1:1, 오버레이/로딩/원본노출방지)
        final croppedFile = await ImageCropUtils.cropImage(
          context: context,
          imagePath: paddedFile.path,
          shape: CropShape.roundedSquare,
          aspectRatio: 1.0,
        );

        if (croppedFile != null) {
          setState(() {
            _croppedImagePath = croppedFile.path;
            _selectedImage = null;
          });
        } else {
          // 크롭을 취소한 경우 이미지를 표시하지 않음
          setState(() {
            _selectedImage = null;
            _croppedImagePath = null;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        ToastUtils.showError(
          context,
          '이미지 선택 중 오류가 발생했습니다: $e',
        );
      }
    }
  }

  /// 기존 이미지를 다시 크롭합니다
  Future<void> _recropImage() async {
    if (_originalPaddedImagePath == null) return;

    try {
      final croppedFile = await ImageCropUtils.cropImage(
        context: context,
        imagePath: _originalPaddedImagePath!,
        shape: CropShape.roundedSquare,
        aspectRatio: 1.0,
      );

      if (croppedFile != null) {
        setState(() {
          _croppedImagePath = croppedFile.path;
        });
      }
    } catch (e) {
      if (mounted) {
        ToastUtils.showError(
          context,
          '이미지 크롭 중 오류가 발생했습니다: $e',
        );
      }
    }
  }

  Future<String?> _saveImageToInternalStorage(XFile image) async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final productImagesDir = Directory(
        path.join(appDir.path, 'product_images'),
      );

      if (!await productImagesDir.exists()) {
        await productImagesDir.create(recursive: true);
      }

      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileName = 'SOGOM_IMG_$timestamp.jpg';
      final savedImage = File(path.join(productImagesDir.path, fileName));

      // 이미지 바이트 읽기
      final imageBytes = await image.readAsBytes();
      final img.Image? imgDecoded = img.decodeImage(imageBytes);
      final img.Image imgResized = img.copyResize(imgDecoded!, width: 400, height: 400);
      final jpgBytes = img.encodeJpg(imgResized, quality: 80);
      await savedImage.writeAsBytes(jpgBytes);
      return savedImage.path;
    } catch (e) {
      if (mounted) {
        ToastUtils.showError(
          context,
          '이미지 저장 중 오류가 발생했습니다: $e',
        );
      }
      return null;
    }
  }

  Future<void> _saveProduct() async {
    if (!mounted) return;

    LoggerUtils.methodStart('_saveProduct', tag: 'RegisterProductScreen');

    if (!_formKey.currentState!.validate()) {
      return;
    }

    // 현재 선택된 행사 확인
    final currentWorkspace = ref.read(currentWorkspaceProvider);
    if (currentWorkspace == null) {
      ToastUtils.showError(context, '현재 선택된 행사가 없습니다. 행사를 선택해주세요.');
      return;
    }

    // EventWorkspace를 Event로 변환
    final currentEvent = EventWorkspaceUtils.workspaceToEvent(currentWorkspace);
    if (currentEvent == null) {
      ToastUtils.showError(context, '행사 정보를 불러올 수 없습니다.');
      return;
    }

    // 수정 모드에서 판매자 변경 감지
    bool sellerChanged = false;
    if (_currentProduct != null &&
        _currentProduct!.sellerName != _selectedSellerName &&
        _selectedSellerName != null) {
      sellerChanged = true;

      // 판매자 변경 경고 다이얼로그 표시
      final shouldProceed = await _showSellerChangeWarning();
      if (!shouldProceed) {
        return;
      }
    }

    // 수정 모드에서 상품명 변경 감지
    bool nameChanged = false;
    if (_currentProduct != null &&
        _currentProduct!.name != _nameController.text) {
      nameChanged = true;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // 이미지 저장 처리
      String? savedImagePath;
      if (_croppedImagePath != null) {
        // 크롭된 이미지가 있으면 그대로 사용
        savedImagePath = _croppedImagePath;
      } else if (_selectedImage != null) {
        savedImagePath = await _saveImageToInternalStorage(_selectedImage!);
      }

      // 카테고리 선택 검증
      if (_selectedCategoryId == null) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('카테고리를 선택해주세요'), backgroundColor: Colors.red),
          );
        }
        return;
      }

      final product = Product(
        id: _currentProduct?.id ?? DateTime.now().millisecondsSinceEpoch,
        name: _nameController.text,
        price: int.parse(_priceController.text),
        quantity: int.parse(_quantityController.text),
        sellerName: _selectedSellerName,
        imagePath: savedImagePath ?? _currentProduct?.imagePath, // 기존 이미지 유지
        eventId: _currentProduct?.eventId ?? currentEvent.id ?? currentWorkspace.id, // 현재 행사 ID 설정
        categoryId: _selectedCategoryId!, // null 체크 후 사용
      );

      // 상세한 저장 로그
      LoggerUtils.logInfo('=== 상품 저장 시작 ===', tag: 'RegisterProductScreen');
      LoggerUtils.logInfo('상품명: ${product.name}', tag: 'RegisterProductScreen');
      LoggerUtils.logInfo('선택된 카테고리 ID: ${product.categoryId}', tag: 'RegisterProductScreen');
      LoggerUtils.logInfo('상품 ID: ${product.id}', tag: 'RegisterProductScreen');
      LoggerUtils.logInfo('수정 모드: ${_currentProduct != null}', tag: 'RegisterProductScreen');
      
      // 카테고리 정보 추가 로그
      final categoriesAsync = ref.read(categoryNotifierProvider);
      if (categoriesAsync.hasValue) {
        final allCategories = categoriesAsync.value!;
        final selectedCategory = allCategories.firstWhere(
          (cat) => cat.id == product.categoryId,
          orElse: () => throw Exception('카테고리를 찾을 수 없습니다: ${product.categoryId}')
        );
        LoggerUtils.logInfo('저장될 카테고리: ${selectedCategory.name} (ID: ${selectedCategory.id})', tag: 'RegisterProductScreen');
      }

      await ErrorUtils.wrapError(
        context,
        () async {
          if (mounted) {
            if (_currentProduct == null) {
              // 상품 추가 시 ProductNotifier만 사용 (단순화)
              await ref.read(productNotifierProvider.notifier).addProduct(product);
            } else {
              // 판매자 변경 또는 상품명 변경 시 연관 기록 업데이트 포함
              await ref.read(productNotifierProvider.notifier).updateProduct(
                product,
                updateRelatedSalesLogs: sellerChanged || nameChanged
              );

              // 간단하고 명확한 완료 메시지 표시
              if (mounted) {
                ToastUtils.showToast(
                  context,
                  _currentProduct == null
                      ? '${product.name} 상품이 등록되었습니다.'
                      : '${product.name} 상품이 수정되었습니다.',
                  duration: ToastUtils.shortDuration,
                );
              }
            }
          }

          if (mounted) {
            Navigator.of(context).pop(true);
          }
        },
        errorMessage: _currentProduct == null
            ? '상품을 등록하는 중 오류가 발생했습니다'
            : '상품을 수정하는 중 오류가 발생했습니다',
        type: ErrorType.database,
        tag: 'RegisterProductScreen',
      );
    } catch (e) {
      if (mounted) {
        ToastUtils.showError(
          context,
          '상품 저장 중 오류가 발생했습니다: $e',
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }

    LoggerUtils.methodEnd('_saveProduct', tag: 'RegisterProductScreen');
  }

  /// 판매자 변경 시 경고 다이얼로그를 표시합니다.
  Future<bool> _showSellerChangeWarning() async {
    return await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('판매자 변경 확인'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('상품 "${_currentProduct!.name}"의 판매자가 변경됩니다.'),
              const SizedBox(height: 8),
              const Text(
                '이 변경은 기존 판매 기록과 통계에도 반영됩니다.',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              const Text(
                '• 기존 판매 기록의 판매자 정보가 업데이트됩니다\n'
                '• 판매 통계가 새로운 판매자 기준으로 재계산됩니다\n'
                '• 이 작업은 되돌릴 수 없습니다',
                style: TextStyle(fontSize: 12),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('취소'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
              ),
              child: const Text('계속 진행'),
            ),
          ],
        );
      },
    ) ?? false;
  }

  Widget _buildImageSection() {
    String? imagePath;
    if (_croppedImagePath != null) {
      imagePath = _croppedImagePath;
    } else if (_selectedImage != null) {
      imagePath = _selectedImage!.path;
    } else if (_currentProduct?.imagePath != null && _currentProduct!.imagePath!.isNotEmpty) {
      imagePath = _currentProduct!.imagePath;
    }

    return Column(
      children: [
        GestureDetector(
          onTap: imagePath != null && _originalPaddedImagePath != null ? _recropImage : null,
          child: Container(
            width: 200,
            height: 200,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(12),
            ),
            child: imagePath != null
                ? ClipRRect(
                    borderRadius: BorderRadius.circular(12),
                    child: AspectRatio(
                      aspectRatio: 1.0, // 정사각형 비율 유지
                      child: ProductImage(
                        imagePath: imagePath,
                        fit: BoxFit.cover, // 높은 품질의 크롭 이미지
                      ),
                    ),
                  )
                : const Icon(Icons.image, size: 64, color: Colors.grey),
          ),
        ),
        const SizedBox(height: 4),
        if (imagePath != null && _originalPaddedImagePath != null) ...[
          Text(
            '사진을 터치하여 크롭 영역을 다시 지정하세요.',
            style: Theme.of(context).textTheme.bodyMedium!.copyWith(fontFamily: 'Pretendard', fontSize: 12, color: Colors.grey),
            textAlign: TextAlign.center,
          ),
        ] else ...[
          const SizedBox(height: 16),
        ],
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text(widget.isEditing ? '상품 수정' : '상품 등록')),
      body: SafeArea(
        child: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : SingleChildScrollView(
                padding: const EdgeInsets.all(16.0),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      TextFormField(
                        controller: _nameController,
                        focusNode: _nameFocusNode,
                        textInputAction: TextInputAction.next,
                        onFieldSubmitted: (value) {
                          _priceFocusNode.requestFocus();
                        },
                        decoration: const InputDecoration(
                          labelText: '상품명',
                          hintText: '상품명을 입력하세요',
                        ),
                        validator: (value) {
                        if (value == null || value.isEmpty) {
                          return '상품명을 입력해주세요';
                        }
                        if (value.length < 2) {
                          return '상품명은 2자 이상이어야 합니다';
                        }
                        if (value.length > 50) {
                          return '상품명은 50자 이하여야 합니다';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),
                    
                    // 카테고리 선택 드롭다운
                    Consumer(
                      builder: (context, ref, child) {
                        final categoriesAsync = ref.watch(categoryNotifierProvider);
                        
                        return categoriesAsync.when(
                          loading: () => DropdownButtonFormField<int>(
                            decoration: const InputDecoration(
                              border: OutlineInputBorder(),
                              labelText: '카테고리 선택',
                            ),
                            items: const [],
                            onChanged: null,
                          ),
                          error: (error, _) => DropdownButtonFormField<int>(
                            decoration: const InputDecoration(
                              border: OutlineInputBorder(),
                              labelText: '카테고리 선택',
                              errorText: '카테고리 로드 실패',
                            ),
                            items: const [],
                            onChanged: null,
                          ),
                          data: (categories) {
                            // 카테고리 목록이 비어있는지 확인
                            if (categories.isEmpty) {
                              return DropdownButtonFormField<int>(
                                decoration: const InputDecoration(
                                  border: OutlineInputBorder(),
                                  labelText: '카테고리 선택',
                                  errorText: '카테고리가 없습니다',
                                ),
                                items: const [],
                                onChanged: null,
                              );
                            }
                            
                            // 새 상품 등록이고 아직 카테고리가 초기화되지 않은 경우에만 설정
                            if (!_categoryInitialized && widget.product == null) { // 새 상품 등록시에만
                              WidgetsBinding.instance.addPostFrameCallback((_) {
                                if (mounted) {
                                  setState(() {
                                    // 정렬 순서가 가장 낮은 카테고리를 기본값으로 설정 (보통 첫번째)
                                    final sortedCategories = [...categories]
                                      ..sort((a, b) => a.sortOrder.compareTo(b.sortOrder));
                                    _selectedCategoryId = sortedCategories.first.id;
                                    _categoryInitialized = true;
                                    LoggerUtils.logInfo('카테고리 기본값 설정: ${sortedCategories.first.name} (ID: ${sortedCategories.first.id})', tag: 'RegisterProductScreen');
                                  });
                                }
                              });
                            }
                            
                            return DropdownButtonFormField<int>(
                              value: _selectedCategoryId,
                              decoration: const InputDecoration(
                                border: OutlineInputBorder(),
                                labelText: '카테고리 선택',
                              ),
                              items: categories.map((category) {
                                return DropdownMenuItem<int>(
                                  value: category.id,
                                  child: Text(category.name),
                                );
                              }).toList(),
                              onChanged: (value) {
                                setState(() {
                                  _selectedCategoryId = value;
                                  _categoryInitialized = true; // 사용자가 선택했으므로 초기화 완료
                                  
                                  // 선택된 카테고리 상세 정보 로그
                                  final selectedCategory = categories.firstWhere((cat) => cat.id == value);
                                  LoggerUtils.logInfo('카테고리 선택됨:', tag: 'RegisterProductScreen');
                                  LoggerUtils.logInfo('- ID: $value', tag: 'RegisterProductScreen');
                                  LoggerUtils.logInfo('- 이름: ${selectedCategory.name}', tag: 'RegisterProductScreen');
                                  LoggerUtils.logInfo('- 정렬순서: ${selectedCategory.sortOrder}', tag: 'RegisterProductScreen');
                                });
                              },
                              validator: (value) {
                                if (value == null) {
                                  return '카테고리를 선택해주세요';
                                }
                                return null;
                              },
                            );
                          },
                        );
                      },
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _priceController,
                      focusNode: _priceFocusNode,
                      textInputAction: TextInputAction.next,
                      onFieldSubmitted: (value) {
                        _quantityFocusNode.requestFocus();
                      },
                      keyboardType: TextInputType.number,
                      inputFormatters: [
                        FilteringTextInputFormatter.digitsOnly,
                      ],
                      decoration: const InputDecoration(
                        labelText: '가격',
                        hintText: '가격을 입력하세요',
                        suffixText: '원',
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return '가격을 입력해주세요';
                        }
                        final price = int.tryParse(value);
                        if (price == null) {
                          return '올바른 가격을 입력해주세요';
                        }
                        if (price < 0) {
                          return '가격은 0 이상이어야 합니다';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _quantityController,
                      focusNode: _quantityFocusNode,
                      textInputAction: TextInputAction.done,
                      keyboardType: TextInputType.number,
                      inputFormatters: [
                        FilteringTextInputFormatter.digitsOnly,
                      ],
                      decoration: const InputDecoration(
                        labelText: '수량',
                        hintText: '수량을 입력하세요',
                        suffixText: '개',
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return '수량을 입력해주세요';
                        }
                        final quantity = int.tryParse(value);
                        if (quantity == null) {
                          return '올바른 수량을 입력해주세요';
                        }
                        if (quantity < 0) {
                          return '수량은 0 이상이어야 합니다';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),
                    // 판매자 선택
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '판매자',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        const SizedBox(height: 8),
                        Consumer(
                          builder: (context, ref, child) {
                            // 실시간 판매자 목록 감시
                            final sellerAsync = ref.watch(sellerNotifierProvider);
                            final sellers = sellerAsync.isLoading
                                ? <Seller>[]
                                : sellerAsync.hasError
                                    ? <Seller>[]
                                    : sellerAsync.sellers;

                            // 판매자 상태 변경 리스너 추가
                            ref.listen<SellerState>(
                              sellerNotifierProvider,
                              (previous, next) {
                                  // 판매자 목록이 변경되었을 때 UI 갱신
                                if (mounted && !next.isLoading && !next.hasError) {
                                    setState(() {
                                      // 현재 선택된 판매자가 삭제된 경우 초기화
                                      if (_selectedSellerName != null && 
                                        !next.sellers.any((s) => s.name == _selectedSellerName)) {
                                        _selectedSellerName = null;
                                      }
                                    });
                                }
                              },
                            );

                            // 디버깅: 판매자 목록 상태 로그
                            LoggerUtils.logDebug(
                              '상품등록 화면 - 판매자 목록 수: ${sellers.length}', 
                              tag: 'RegisterProductScreen'
                            );
                            for (var seller in sellers) {
                              LoggerUtils.logDebug(
                                '판매자: ${seller.name} (ID: ${seller.id})', 
                                tag: 'RegisterProductScreen'
                              );
                            }

                            return DropdownButtonFormField<String>(
                              value: _selectedSellerName,
                              decoration: const InputDecoration(
                                border: OutlineInputBorder(),
                                labelText: '판매자 선택',
                              ),
                              items: sellers.map((seller) {
                                final isDefault = seller.isDefault;
                                return DropdownMenuItem<String>(
                                  value: seller.name,
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min, // 추가
                                    children: [
                                      if (isDefault) 
                                        const Icon(Icons.star, color: Colors.amber, size: 16),
                                      const SizedBox(width: 8),
                                      Text(
                                        seller.name,
                                        style: TextStyle(
                                          fontWeight: isDefault ? FontWeight.bold : FontWeight.normal,
                                        ),
                                      ),
                                      if (isDefault)
                                        Container(
                                          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                                          decoration: BoxDecoration(
                                            color: Colors.amber.withValues(alpha: 0.2),
                                            borderRadius: BorderRadius.circular(8),
                                          ),
                                          child: const Text(
                                            '대표',
                                            style: TextStyle(
                                              fontSize: 10,
                                              color: Colors.amber,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                        ),
                                    ],
                                  ),
                                );
                              }).toList(),
                              onChanged: (value) {
                                setState(() {
                                  _selectedSellerName = value;
                                });
                              },
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return '판매자를 선택해주세요';
                                }
                                return null;
                              },
                            );
                          },
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),

                    // 이미지 섹션
                    _buildImageSection(),
                    const SizedBox(height: 8),

                    // 사진 선택 버튼
                    ElevatedButton(
                      onPressed: _selectImage,
                      child: const Text('사진 선택'),
                    ),
                    const SizedBox(height: 24),

                    // 저장 버튼
                    ElevatedButton(
                      onPressed: _isLoading ? null : _saveProduct,
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                      ),
                      child: Text(widget.isEditing ? '수정 완료' : '저장하기'),
                    ),
                    const SizedBox(height: 16),
                  ],
                ),
              ),
            ),
        ),
      );
    }
  }

